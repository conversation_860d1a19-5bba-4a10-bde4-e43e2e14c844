<template>
  <view class="page-nav-top-common-bg1">
    <NavBar title="新增操作票" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view style="height: 98%" scroll-y show-scrollbar>
      <u-form :model="state.form" ref="formRef" label-width="210" :error-type="['border-bottom', 'toast']" class="form-container">
        <panelTitle title="基础信息" />
        <view class="com-card details-card-box" style="padding: 0 28rpx;">
          <u-form-item label="操作票编码" prop="operateCode" required>
            <u-input v-model="state.form.operateCode" placeholder="请输入" maxlength="128" @change="checkOperateCodeLength" />
          </u-form-item>

          <u-form-item label="工程名称" prop="projectId" required>
            <u-input
              v-model="state.select.current1.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'projectName'
                  state.select.list = state.projectOptions
                }
              "
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item label="操作人" prop="operateUserId" required>
            <u-input
              v-model="state.select.current2.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'operateUser'
                  state.select.list = state.userOptions
                }
              "
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item label="负责人" prop="guardianUserId" required>
            <u-input
              v-model="state.select.current3.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'guardianUser'
                  state.select.list = state.userOptions
                }
              "
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item label="操作开始时间" prop="startDate" required :border-bottom="true">
            <u-input type="input" v-model="state.form.startDate" placeholder="请选择" disabled></u-input>
            <u-icon name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('start')"></u-icon>
          </u-form-item>

          <u-form-item label="操作结束时间" prop="endDate" required :border-bottom="true">
            <u-input type="input" v-model="state.form.endDate" placeholder="请选择" disabled></u-input>
            <u-icon name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('end')"></u-icon>
          </u-form-item>

          <u-form-item label="备注">
            <u-input v-model="state.form.remark" placeholder="请输入" maxlength="512" @change="checkRemarkLength" />
          </u-form-item>
        </view>

        <view class="operation-content-section">
          <view class="operation-header">
            <panelTitle title="操作内容" />
            <view class="add-operation-btn" @click="addOperationContent">
              <text class="add-text">新增+</text>
            </view>
          </view>
          
          <view class="com-card details-card-box" style="padding: 10rpx 28rpx;">
            <view class="operation-list" v-if="state.operationContents.length > 0">
              <view class="operation-item" v-for="(item, index) in state.operationContents" :key="index">
                <view class="operation-item-header">
                  <text class="operation-label">内容{{ index + 1 }}：</text>
                  <u-icon 
                    name="close" 
                    color="#FF4D4F" 
                    size="32" 
                    @click="deleteOperationContent(index)"
                    class="delete-icon"
                  />
                </view>
                <u-input
                  v-model="item.content"
                  placeholder="请输入操作内容"
                  type="textarea"
                  :maxlength="500"
                  class="operation-input"
                />
              </view>
            </view>
            
            <view class="no-operation" v-else>
              <text class="no-operation-text">暂无操作内容</text>
            </view>
          </view>
        </view>
      </u-form>
    </scroll-view>

    <view class="form-btn-group">
      <u-button class="form-sure-btn receive-btn" @click="handleSubmit">提交</u-button>
    </view>

    <u-select
      v-model="state.select.show"
      mode="single-column"
      :list="state.select.list"
      @cancel="state.select.show = false"
      @confirm="onSelectConfirm"
    ></u-select>

    <u-picker
      v-model="state.showPickerTime"
      mode="time"
      :default-time="dayjs().format('YYYY-MM-DD HH:mm')"
      :params="state.timeParams"
      @cancel="state.showPickerTime = false"
      @confirm="onTimeConfirm"
    ></u-picker>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getDispatchProjectList, getDispatchUser, addOperateCmd } from '../services'
import dayjs from 'dayjs'
import { useRouter } from 'uni-mini-router'

const router = useRouter()
const formRef = ref(null)
const uNotifyRef = ref()

const props = defineProps({
  runCmd: {
    type: String,
    default: ''
  }
})

const state = reactive({
  select: {
    show: false,
    field: undefined,
    current1: { label: undefined, value: undefined },
    current2: { label: undefined, value: undefined },
    current3: { label: undefined, value: undefined },
    list: [],
  },
  showPickerTime: false,
  timeParams: {
    year: true,
    month: true,
    day: true,
    hour: true,
    minute: true,
  }, 
  projectOptions: [],
  userOptions: [],

  // 操作内容数组
  operationContents: [
    { content: '检查管理范围内河道是否存在游泳、捕鱼和船只水上作业等可能危机人身安全的情况' },
    { content: '检查进水口拦污栅是否通畅' }
  ],

  form: {
    operateCode: '',
    projectId: undefined,
    operateUserId: undefined,
    guardianUserId: undefined,
    startDate: '',
    endDate: '',
    remark: ''
  },

  rules: {
    operateCode: [
      { required: true, message: '请输入操作票编码' },
      { max: 128, message: '操作票编码限制128字符' }
    ],
    projectId: [{ required: true, message: '请选择工程名称' }],
    operateUserId: [{ required: true, message: '请选择操作人' }],
    guardianUserId: [{ required: true, message: '请选择负责人' }],
    startDate: [{ required: true, message: '请选择开始时间' }],
    endDate: [{ required: true, message: '请选择结束时间' }],
    remark: [{ max: 512, message: '限制512字符' }]
  }
})

onMounted(() => {
  formRef.value.setRules(state.rules)

  // 获取工程列表
  getDispatchProjectList().then(res => {
    state.projectOptions = (res?.data || [])?.map(el => ({
      ...el,
      label: el.projectName,
      value: el.projectId,
    }))
  })
})

// 获取用户列表
const getUsersById = id => {
  getDispatchUser({ dispatchProjectId: id }).then(res => {
    state.userOptions = res?.data?.map(el => ({
      label: el.name,
      value: el.userId + '',
    }))
  })
}

const onSelectConfirm = item => {
  switch (state.select.field) {
    case 'projectName':
      state.select.current1 = item[0]
      state.form.projectId = item[0].value
      getUsersById(state.projectOptions.find(el => el.projectId == item[0].value)?.dispatchProjectId)
      // 清空已选择的人员
      state.select.current2.label = undefined
      state.select.current2.value = undefined
      state.form.operateUserId = undefined
      state.select.current3.label = undefined
      state.select.current3.value = undefined
      state.form.guardianUserId = undefined
      return
    case 'operateUser':
      state.select.current2 = item[0]
      state.form.operateUserId = item[0].value
      return
    case 'guardianUser':
      state.select.current3 = item[0]
      state.form.guardianUserId = item[0].value
      return
    default:
      return
  }
}

const onOpenTimePicker = (type) => {
  state.pickType = type
  state.showPickerTime = true
}

const onTimeConfirm = p => {
  const timeStr = `${p.year}-${p.month}-${p.day} ${p.hour}:${p.minute}`
  if (state.pickType === 'start') {
    state.form.startDate = timeStr
  } else {
    timeComparison(state.form.startDate, timeStr)
    state.form.endDate = timeStr
  }
}

const timeComparison = (startTime, endTime) => {
  if (endTime < startTime) {
    uNotifyRef.value.show({
      type: 'warning',
      title: '结束时间不能小于开始时间',
      duration: 800,
    })
    state.form.endDate = ''
    return
  }
}

// 新增操作内容
const addOperationContent = () => {
  state.operationContents.push({ content: '' })
}

// 删除操作内容
const deleteOperationContent = (index) => {
  if (state.operationContents.length > 1) {
    state.operationContents.splice(index, 1)
  } else {
    uni.showToast({
      title: '至少保留一项操作内容',
      icon: 'none',
      duration: 2000
    })
  }
}

const handleSubmit = () => {
  // 验证操作内容
  if (state.operationContents.length === 0) {
    uni.showToast({
      title: '请至少添加一项操作内容',
      icon: 'none',
      duration: 2000
    })
    return
  }

  for (let i = 0; i < state.operationContents.length; i++) {
    if (!state.operationContents[i].content || state.operationContents[i].content.trim() === '') {
      uni.showToast({
        title: `第${i + 1}项操作内容不能为空`,
        icon: 'none',
        duration: 2000
      })
      return
    }
  }

  formRef.value.validate(valid => {
    if (valid) {
      // 构建请求参数
      const params = {
        operateCode: state.form.operateCode,
        projectId: state.form.projectId,
        startDate: state.form.startDate + ':00',
        endDate: state.form.endDate + ':00',
        remark: state.form.remark,
        operateName: state.select.current2.label, // 操作人姓名
        guardianName: state.select.current3.label, // 负责人姓名
        isOpen: 0, // 默认值
        items: state.operationContents.map(operation => {
          return {
            content: operation.content,
            status: 1 // 默认选中状态
          }
        }),
        operateDate: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 当前时间作为操作日期
        cmdCode: props.runCmd, // 使用操作票编码作为指令编码
      }
      // 调用接口
      addOperateCmd(params).then(res => {
        if (res.code === 200) {
          uNotifyRef.value.show({
            type: 'success',
            title: '提交成功',
            duration: 800,
          })
          setTimeout(() => {
            router.push({
              path: '/pages/dispatch-instruction/index',
            })
          }, 800)
        } else {
          uNotifyRef.value.show({
            type: 'error',
            title: res.msg || '提交失败',
            duration: 800,
          })
        }
      }).catch(err => {
        uNotifyRef.value.show({
          type: 'error',
          title: '提交失败',
          duration: 800,
        })
        console.error('提交操作票失败:', err)
      })
    }
  })
}

// 检查操作票编码长度
const checkOperateCodeLength = () => {
  if (state.form.operateCode.length > 128) {
    uNotifyRef.value.show({
      type: 'warning',
      title: '操作票编码限制128字符',
      duration: 1500,
    })
    state.form.operateCode = state.form.operateCode.slice(0, 128)
  }
}

// 检查备注长度
const checkRemarkLength = () => {
  if (state.form.remark.length > 512) {
    uNotifyRef.value.show({
      type: 'warning',
      title: '限制512字符',
      duration: 1500,
    })
    state.form.remark = state.form.remark.slice(0, 512)
  }
}
</script>

<style lang="scss" scoped>
.operation-content-section {
  margin-top: 20rpx;
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;

  .add-operation-btn {
    .add-text {
      color: #3772FF;
      font-size: 28rpx;
    }
  }
}

.operation-list {
  .operation-item {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .operation-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .operation-label {
        color: #1D2129;
        font-size: 28rpx;
        font-weight: 500;
      }

      .delete-icon {
        padding: 10rpx;
      }
    }

    .operation-input {
      :deep(.u-input__content) {
        min-height: 120rpx;
      }
    }
  }
}

.no-operation {
  text-align: center;
  padding: 60rpx 0;

  .no-operation-text {
    color: #86909C;
    font-size: 28rpx;
  }
}

.form-container {
  padding-bottom: 20rpx;
}

.textarea {
  :deep(.u-form-item__body) {
    display: block;
  }
  :deep(.u-form-item--left) {
    width: 100% !important;
  }
  :deep(.u-form-item--right) {
    height: 268rpx;
    border-radius: 8rpx;
    background: #f2f3f5;
    border: 1px solid #e5e6eb;
  }
}

.form-btn-group {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0 40rpx 0;

  .receive-btn {
    width: calc(100% - 60rpx) !important;
    border-radius: 60rpx;
  }
  .form-cancel-btn {
    background: #F2F3F5;
    color: #4E5969;
  }
}
</style>
