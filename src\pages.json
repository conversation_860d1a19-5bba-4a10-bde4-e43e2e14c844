{
  "pages": [
    {
      "path": "pages/login/loadingLogin"
    },
    {
      "path": "pages/login/index",
      "name": "login"
    },
    {
      "path": "pages/home/<USER>",
      "name": "home"
    },
    {
      "path": "pages/user/index",
      "name": "user"
    },
    {
      "path": "pages/user/edit-user/index",
      "name": "editUser"
    },
    {
      "path": "pages/user/edit-user/edit-user-info/index",
      "name": "editUserInfo"
    },
    {
      "path": "pages/user/edit-password/index",
      "name": "editPassword"
    },
    {
      "path": "pages/middle-page/success/index",
      "name": "success"
    },
    {
      "path": "pages/middle-page/failure/index",
      "name": "failure"
    },
    {
      "path": "pages/patrol/patrol-list/index",
      "name": "patrolList"
    },
    {
      "path": "pages/patrol/patrol-detail/index",
      "name": "patrolDetail"
    },
    {
      "path": "pages/patrol/patrol-content/index",
      "name": "patrolContent"
    },
    {
      "path": "pages/monitor-center/index",
      "name": "monitorCenter"
    },
    {
      "path": "pages/monitor-center/monitor-list/index",
      "name": "monitorList"
    },
    {
      "path": "pages/monitor-center/monitor-detail/index",
      "name": "monitorDetail"
    },
    {
      "path": "pages/brief-report/report-list/index",
      "name": "reportList"
    },
    {
      "path": "pages/brief-report/report-detail/index",
      "name": "reportDetail"
    },
    {
      "path": "pages/warehouse/inventory-query/index",
      "name": "inventoryQuery"
    },
    {
      "path": "pages/warehouse/inventory-query/detail/index",
      "name": "inventoryQueryDetail"
    },
    {
      "path": "pages/warehouse/inventory-check/index",
      "name": "inventoryCheck"
    },
    {
      "path": "pages/warehouse/inventory-check/detail/index",
      "name": "inventoryCheckDetail"
    },
    {
      "path": "pages/warehouse/inventory-check/add/index",
      "name": "inventoryCheckAdd"
    },
    {
      "path": "pages/warehouse/in-warehouse/index",
      "name": "inWarehouse"
    },
    {
      "path": "pages/warehouse/in-warehouse/detail/index",
      "name": "inWarehouseDetail"
    },
    {
      "path": "pages/warehouse/in-warehouse/add/index",
      "name": "inWarehouseAdd"
    },
    {
      "path": "pages/warehouse/out-warehouse/index",
      "name": "outWarehouse"
    },
    {
      "path": "pages/warehouse/out-warehouse/detail/index",
      "name": "outWarehouseDetail"
    },
    {
      "path": "pages/warehouse/out-warehouse/add/index",
      "name": "outWarehouseAdd"
    },
    {
      "path": "pages/warehouse/allot/index",
      "name": "allot"
    },
    {
      "path": "pages/warehouse/allot/record/index",
      "name": "allotRecord"
    },
    {
      "path": "pages/warehouse/allot/detail/index",
      "name": "allotDetail"
    },
    {
      "path": "pages/warehouse/allot/add/index",
      "name": "allotAdd"
    },
    {
      "path": "pages/duty-management/rota/index",
      "name": "rota"
    },
    {
      "path": "pages/duty-management/rota-form/index",
      "name": "rotForm"
    },
    {
      "path": "pages/duty-management/rota-detail/index",
      "name": "rotaDetail"
    },
    {
      "path": "pages/warning-center/index",
      "name": "warningCenter"
    },
    {
      "path": "pages/warning-center/record/index",
      "name": "warningRecord"
    },
    {
      "path": "pages/video-center/index",
      "name": "videoCenter"
    },
    {
      "path": "pages/video-center/item-video/index",
      "name": "itemVideo"
    },
    {
      "path": "pages/water-scheduling/index",
      "name": "waterScheduling"
    },
    {
      "path": "pages/water-scheduling/to-review/index",
      "name": "waterToReview"
    },
    {
      "path": "pages/water-scheduling/details/index",
      "name": "waterSchedulingDetails"
    },
    {
      "path": "pages/work-monitor/index",
      "name": "workMonitor"
    },
    {
      "path": "pages/work-monitor/detail/index",
      "name": "workMonitorDetail"
    },
    {
      "path": "pages/maintenance-record/index",
      "name": "maintenanceRecord"
    },
    {
      "path": "pages/maintenance-record/add/index",
      "name": "maintenanceRecordAdd"
    },
    {
      "path": "pages/maintenance-record/details/index",
      "name": "maintenanceRecordDetails"
    },
    {
      "path": "pages/emergency-repair/index",
      "name": "emergencyRepair"
    },
    {
      "path": "pages/emergency-repair/add/index",
      "name": "emergencyRepairAdd"
    },
    {
      "path": "pages/emergency-repair/details/index",
      "name": "emergencyRepairDetails"
    },
    {
      "path": "pages/hydropower-dispatch/index",
      "name": "hydropowerDispatch"
    },
    {
      "path": "pages/hydropower-dispatch/add/index",
      "name": "hydropowerDispatchAdd"
    },
    {
      "path": "pages/hydropower-dispatch/details/index",
      "name": "hydropowerDispatchDetails"
    },

    {
      "path": "pages/demo/index"
    },
    {
      "path": "pages/dispatch-instruction/index",
      "name": "dispatchInstruction"
    },
    {
      "path": "pages/dispatch-instruction/run-add/index"
    },
    {
      "path": "pages/dispatch-instruction/run-details/index"
    },
    {
      "path": "pages/dispatch-instruction/operation-ticket/index"
    },
    {
      "path": "pages/dispatch-instruction/operation-ticket-add/index"
    },
    {
      "path": "pages/dispatch-instruction/operation-ticket-details/index"
    },
    {
      "path": "components/uni-upgrade-center-app/pages/upgrade-popup",
      "style": {
        "disableScroll": true,
        "app-plus": {
          "backgroundColorTop": "transparent",
          "background": "transparent",
          "titleNView": false,
          "scrollIndicator": false,
          "popGesture": "none",
          "animationType": "fade-in",
          "animationDuration": 200
        }
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "transparent",
    "navigationStyle": "custom",
    "app-plus": {
      "titleView": false
    },
    "backgroundColor": "transparent"
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^u-(.*)": "vk-uview-ui/components/u-$1/u-$1.vue",
      "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"
    }
  },

  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#007AFF",
    "borderStyle": "#fff",
    "backgroundColor": "#fff",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "iconPath": "static/images/tabBar/home.png",
        "selectedIconPath": "static/images/tabBar/home_active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/user/index",
        "iconPath": "static/images/tabBar/me.png",
        "selectedIconPath": "static/images/tabBar/me_active.png",
        "text": "我的"
      }
    ]
  },

  "condition": {
    //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "", //启动页面，必选
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
