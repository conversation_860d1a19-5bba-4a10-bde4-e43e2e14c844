<template>
  <view class="page-details-bg">
    <NavBar title="操作指令详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
    <view class="com-card details-card-box" style="height: 604rpx">
      <view class="card-title details-card-title">{{ state.details.operateCode }}</view>
      <view class="details-body">
        <view class="details-card">
          <view class="card-row">
            <text class="label">操作令编号</text>
            <text class="value">{{ state.details.operateCode || '--' }}</text>
          </view>
          <view class="card-row">
            <text class="label">工程名称</text>
            <text class="value">{{ state.details.projectName || '--' }}</text>
          </view>
          <view class="card-row">
            <text class="label">操作日期</text>
            <text class="value">{{ state.details.operateDate || '--' }}</text>
          </view>

          <view class="card-row">
            <text class="label">操作人</text>
            <text class="value">{{ state.details?.operateName || '--' }}</text>
          </view>
          <view class="card-row">
            <text class="label">发令人</text>
            <text class="value">{{ state.details.starterName || '--' }}</text>
          </view>
          <view class="card-row">
            <text class="label">负责人</text>
            <text class="value">{{ state.details.guardianName }}</text>
          </view>
          <view class="card-row">
            <text class="label">操作开始时间</text>
            <text class="value">{{ state.details.startDate }}</text>
          </view>

          <view class="card-row">
            <text class="label">操作结束时间</text>
            <text class="value">{{ state.details?.endDate == null ? '--' : state.details?.endDate }}</text>
          </view>
          <view class="card-row">
            <text class="label">备注</text>
            <text class="value work-time">{{ state.details?.remark == null || state.details?.remark == '' ? '--' : state.details?.remark }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="com-card details-card-box">
      <view class="operation-card">
        <view class="details-list-row">
          <panelTitle title="操作内容" />
        </view>

        <view class="operation-content" v-for="(el, i) in state.details?.operationContentList" :key="i">
          <view class="top">
            <text class="text">内容{{ i + 1 }}：</text>
          </view>
          <view class="content">{{ el.content }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
  import { reactive, onMounted } from 'vue'
  const props = defineProps(['operateCmdId'])

  import { getOperationInstructionDetails, getDispatchProjectList } from '../services'

  const state = reactive({
    details: {
    },
    projectOptions:[]
  })

  onMounted(() => {
    // 使用mock数据
    state.projectOptions = [
      { label: '河道清淤工程', value: '1', projectId: '1', projectName: '河道清淤工程' },
      { label: '水闸维护工程', value: '2', projectId: '2', projectName: '水闸维护工程' }
    ]
    
    // 使用mock详情数据
    const mockDetails = {
      operateCode: 'CZ202401001',
      projectId: '1',
      projectName: '河道清淤工程',
      operateDate: '2024-03-15',
      operateName: '赵六',
      starterName: '李四',
      guardianName: '张三',
      startDate: '2024-03-15 08:30',
      endDate: '2024-03-15 17:30',
      remark: '操作过程中注意安全',
      operationContentList: [
        { content: '检查管理范围内河道是否存在游泳、捕鱼和船只水上作业等可能危机人身安全的情况' },
        { content: '检查进水口拦污栅是否通畅' },
        { content: '检查宁郭塘闸门是否开启' },
        { content: '至10kV 102计量柜，旋转上方的电压转换开关，检查三相电压是否平衡及缺相' },
        { content: '至低压配电室内转动总柜上方电压表旋转开关检查三相总电压是否平衡' }
      ]
    }
    
    state.details = mockDetails
    state.details.projectName = state.projectOptions.find(el => el.value === state.details.projectId)?.label || mockDetails.projectName
  })
</script>
<style lang="scss" scoped>
  .operation-card {
    height: 100%;
  }
  .details-list-row {
    display: flex;
    align-items: center;
    .details-list {
      width: 84rpx;
      height: 84rpx;
      margin-left: auto;
    }
  }
  .operation-content {
    width: 94%;
    margin: 30rpx auto;
    position: relative;
    border-radius: 24rpx;
    background: #eef5ff;
    border: 1px solid #eaf2fd;
    .top {
      display: flex;
      .text {
        color: #1D2129;
        padding: 24rpx 24rpx 0 24rpx;
        font-weight: 500;
        font-size: 28rpx;
      }
    }
    .content {
      padding: 24rpx;
      padding-top: 16rpx;
      color: #1d2129;
      font-size: 30rpx;
      line-height: 1.6;
    }
  }
</style>
