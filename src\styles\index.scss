@import './variables.scss';
@import '@/static/fonts/MyFont.scss';
@import './mixin.scss';

page {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  color: var(--color-text);
  font-family: PingFang SC-Medium;
  box-sizing: border-box;
}

html {
  height: 100%;
}

#app {
  height: 100%;
  background: #edeff1;
}
.page-nav-top-common {
  padding-top: 20rpx;
  background: #edeff1;
}

.page-nav-top-common-bg1 {
  background: #edeff1 url('@/static/images/nav-bar-bg.png') no-repeat;
  background-size: 100%;
  padding-top: 20rpx;
}

.page-nav-top-common-bg1-white {
  background: #ffffff url('@/static/images/nav-bar-bg.png') no-repeat;
  background-size: 100%;
  padding-top: 20rpx;
}

.page-nav-top-common-bg2 {
  background: #edeff1 url('@/static/images/details-top-bg.png') no-repeat;
  background-size: 100%;
  padding-top: 20rpx;
  height: calc(100vh - 20rpx);
}

.page-nav-top-common-bg3 {
  background: #edeff1 url('@/static/images/nav-bar-bg3.png') no-repeat;
  background-size: 100%;
  background-position: 0 -60rpx; /* 向上偏移40rpx */
  // padding-top: 40rpx;
}

.page-details-bg {
  min-height: 100%;
  background: #91cdff;
  box-shadow: 0px 4px 10px 0px rgba(55, 114, 255, 0.05);
}

.page-top-nav {
  margin-top: 40rpx;
}

.com-card {
  padding: 28rpx;
  margin: 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// u-view 样式重写
// 最大号的primary、方形按钮背景样式
.u-btn.u-size-default.u-btn--primary {
  background: linear-gradient(44deg, #3772ff 0%, #5ddcf5 97%);
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(69, 178, 244, 0.25);
  border-radius: 16rpx;
  border: none;
  font-size: 30rpx;
  padding: 30rpx 0;
  height: unset;
  line-height: unset;
}
.u-btn.u-size-default.u-btn--primary.u-btn--primary--disabled {
  opacity: 0.6;
}

// 模块小标题
.u-section__title {
  padding-left: 0 !important;
  margin-left: -10rpx;
  margin-bottom: 12rpx;
  .u-section__title__icon-wrap {
    position: static !important;
    .u-icon__icon.u-iconfont {
      font-size: 34rpx !important;
      position: relative;
      left: -10rpx;
    }
  }
}
// select弹窗标题
.u-select__header__title {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}

// NumberBox 步进器 按钮
.u-numberbox {
  .num-btn {
    top: 0 !important;
  }
}

// 地图
.mapboxgl-ctrl-bottom-left {
  display: none;
}
.mapboxgl-ctrl.mapboxgl-ctrl-attrib.mapboxgl-compact {
  display: none;
}

.list-add-btn {
  margin-right: 30rpx;
  font-size: 50rpx;
  cursor: pointer;
}
.list-search {
  height: 88rpx;
  position: relative;
  .search-icon {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    top: 29rpx;
    left: 70rpx;
    display: inline-block;
    background: url('@/static/images/search-icon.png') 0 0 no-repeat;
    background-size: 100%;
  }
  .search-input {
    width: 92%;
    margin: 0 auto;
    height: 88rpx;
    font-size: 28rpx;
    padding-left: 82rpx !important;
    background: #f7f8fa !important;
    border-radius: 16rpx;
    .u-input__input {
      height: 88rpx;
      line-height: 88rpx;
    }
  }
  .search-close {
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    top: 22rpx;
    right: 70rpx;
    display: inline-block;
    background: url('@/static/images/search-close.png') 0 0 no-repeat;
    background-size: 100%;
  }
}
.com-card {
  position: relative;
  .card-title {
    font-family: PingFang SC;
    font-size: 30rpx;
    font-weight: 500;
    color: #1d2129;
    margin-bottom: 30rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .grey-card {
    border-radius: 8rpx;
    background: #f7f8fa;
    padding: 16rpx 26rpx;
  }
  .card-row {
    line-height: 56rpx;
    display: flex;
    .label {
      color: #4e5969;
      font-size: 28rpx;
    }
    .value {
      max-width: 70%;
      margin-left: auto;
      word-wrap: break-word;
      color: #1d2129;
      font-size: 30rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .work-time {
      white-space: normal; 
      word-break: break-all;
      
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .title {
      color: #4E5969;
      font-size: 30rpx;
      font-weight: 600;
    }
    .tag {
      background-color: #E8FFEA;
      color: #00B42A;
      border: 2rpx solid #00B42A;
      border-radius: 16rpx;
      padding: 8rpx 20rpx;
      font-size: 28rpx;
      line-height: 1;
      margin-left: auto;
    }
  }
  .card-long {
    line-height: 44rpx;
    margin-bottom: 20rpx;
    .label {
      color: #4e5969;
      font-size: 28rpx;
    }
    .value {
      color: #1d2129;
      font-size: 30rpx;
    }
  }
  &.details-card-box {
    padding: 0;
    border-radius: 32rpx;
    .details-card-title {
      position: absolute;
      width: 98.6%;
      height: 160rpx;
      line-height: 94rpx;
      left: 5rpx;
      top: 5rpx;
      padding-left: 20rpx;
      border-radius: 32rpx;
      background: #e9f4ff;
      z-index: 1;
    }
    .details-body {
      padding-top: 94rpx;
    }
    .details-card {
      padding: 28rpx;
      background: #ffffff;
      border-radius: 32rpx;
      position: relative;
      z-index: 99;
    }
    .details-list-card {
      padding: 28rpx 28rpx 0 28rpx;
      background: #ffffff;
      border-radius: 32rpx;
      position: relative;
      z-index: 99;
    }
    .operation-card {
      margin: 4rpx;
      border-radius: 32rpx;
      background: linear-gradient(180deg, #e9f4ff 0%, rgba(233, 244, 255, 0) 3%), #ffffff;
      border: 2px solid #ffffff;
    }
  }
}
.card-btn-group,
.form-btn-group {
  .u-hairline-border:after {
    border: none !important;
  }
}
.card-btn-group {
  display: flex;
  margin-top: 26rpx;
  justify-content: flex-end;
  .u-btn {
    margin-left: 20rpx;
    padding: 0 26rpx !important;
    margin-right: 0 !important;
  }
}
.card-default-btn {
  height: 64rpx !important;
  border-radius: 16rpx;
  padding: 9px 0px;
  background: #ffffff;
  border: 1px solid #e5e6eb !important;
}
.card-active-btn {
  color: #fff !important;
  height: 64rpx !important;
  background-color: #165dff !important;
}
.form-btn-group {
  display: flex;
  padding-bottom: 30rpx;
  .form-cancel-btn {
    width: 330rpx !important;
    height: 96rpx !important;
    border-radius: 16rpx;
    background: #ffffff;
    border: 1px solid #e5e6eb !important;
  }
  .form-sure-btn {
    width: 330rpx !important;
    height: 96rpx !important;
    color: #fff !important;
    border-radius: 16rpx;
    background: #3772ff !important;
  }
}
