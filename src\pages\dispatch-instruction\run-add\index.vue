<template>
  <view class="page-nav-top-common-bg1">
    <NavBar :title="state.navTitle" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view style="height: 98%" scroll-y show-scrollbar>
      <u-form :model="state.form" ref="formRef" label-width="210" :error-type="['border-bottom', 'toast']" class="form-container">
        <!-- 状态图标 -->
        <image v-if="props.type === 'approve'" class="status-icon" src="/static/images/approve-icon.png" mode="aspectFit"></image>
        <image v-if="props.type === 'receive'" class="status-icon" src="/static/images/receive-icon.png" mode="aspectFit"></image>
        
        <!-- 修改状态下保持原样式 -->
        <template v-if="isEdit">
          <panelTitle title="基础信息" />
          <view class="com-card details-card-box" style="padding: 0 28rpx;">
            
            <u-form-item label="调度令编号" prop="cmdCode" required>
              <u-input v-model="state.form.cmdCode" placeholder="请输入调度令编号" :disabled="isReadOnly" />
            </u-form-item>
            
            <u-form-item label="调度令名称" prop="cmdName" required>
              <u-input v-model="state.form.cmdName" placeholder="请输入调度令名称" :disabled="isReadOnly" />
            </u-form-item>

            <u-form-item label="调度方案" prop="dispatchId">
              <u-input
                v-model="state.select.current1.label"
                type="select"
                @click="
                  () => {
                    if (isReadOnly) return;
                    state.select.show = true
                    state.select.field = 'schedulingScheme'
                    state.select.list = state.dispatchOptions
                  }
                "
                placeholder="请选择调度方案"
                :disabled="isReadOnly"
              />
            </u-form-item>

            <u-form-item label="调度类型">
              <u-input
                v-model="state.select.current2.label"
                type="select"
                @click="
                  () => {
                    if (isReadOnly) return;
                    state.select.show = true
                    state.select.field = 'dispatchTypeCode'
                    state.select.list = state.dispatchTypeOptions
                  }
                "
                placeholder="请选择调度类型"
                :disabled="isReadOnly"
              />
            </u-form-item>

            <u-form-item label="计划开始时间" prop="planStartDate" required :border-bottom="true">
              <u-input type="input" v-model="state.form.planStartDate" placeholder="请选择计划开始时间" disabled></u-input>
              <u-icon v-if="!isReadOnly" name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('start', 'plan')"></u-icon>
            </u-form-item>
            <u-form-item label="计划结束时间" prop="planEndDate" required :border-bottom="true">
              <u-input type="input" v-model="state.form.planEndDate" placeholder="请选择计划结束时间" disabled></u-input>
              <u-icon v-if="!isReadOnly" name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('end', 'plan')"></u-icon>
            </u-form-item>
          </view>

          <panelTitle title="工程信息" />
          <view class="com-card" style="padding: 0 30rpx 50rpx 30rpx;">
            <u-form-item label="添加工程" prop="projectList" required :border-bottom="false">
                <view class="add-btn" @click="addProject" v-if="!isReadOnly">
                  <text class="add-text">+ 添加</text>
                </view>
            </u-form-item>
            
            <!-- 工程信息列表 -->
            <view class="project-list" v-if="state.projectList.length > 0">
              <view class="project-item" v-for="(project, index) in state.projectList" :key="index">
                <!-- 工程名称和删除按钮 -->
                <view class="project-row project-name-row">
                  <u-input
                    v-model="project.projectName"
                    type="select"
                    @click="selectProject(index)"
                    placeholder="请选择工程名称"
                    :disabled="isReadOnly"
                    class="project-name-input"
                  /> 
                  <u-icon 
                    v-if="!isReadOnly"
                    name="trash" 
                    color="#FF4D4F" 
                    size="32" 
                    @click="deleteProject(index)"
                    class="delete-icon"
                  />
                </view>
                
                <!-- 工程负责人 -->
                <view class="project-row">
                  <text class="project-label">工程负责人</text>
                  <u-input
                    v-model="project.wardUserName"
                    type="select"
                    @click="selectManager(index)"
                    placeholder="请选择工程负责人"
                    :disabled="isReadOnly"
                    class="project-input"
                  />
                </view>
                

                
                <!-- 备注 -->
                <view class="project-row">
                  <text class="project-label">备注</text>
                  <u-input
                    v-model="project.remark"
                    placeholder="请输入备注信息"
                    :disabled="isReadOnly"
                    class="project-input"
                  />
                </view>
              </view>
            </view>
            
            <!-- 暂无数据 -->
            <view class="no-data" v-else>
              <text class="no-data-text">暂无数据</text>
            </view>
          </view>
        </template>

        <template v-else>
          <view class="com-card details-card-box">
            <view class="card-title details-card-title">{{ state.form.cmdName }}</view>
            <view class="details-body">
              <view class="details-card">
                <view class="card-row">
                  <text class="label">调度令编号</text>
                  <text class="value">{{ state.form.cmdCode }}</text>
                </view>

                <view class="card-row">
                  <text class="label">调度令名称</text>
                  <text class="value">{{ state.form.cmdName }}</text>
                </view>
                <view class="card-row">
                  <text class="label">调度方案</text>
                  <text class="value">{{ state.form.dispatchCode == null ? '--' : state.form.dispatchCode }}</text>
                </view>

                <view class="card-row">
                  <text class="label">调度类型</text>
                  <text class="value">防汛调度</text>
                </view>

                <view class="card-row">
                  <text class="label">计划工作时间</text>
                  <text class="value work-time">{{ state.form.planStartDate }}至{{ state.form.planEndDate }}</text>
                </view>
              </view>
            </view>
          </view>

          <view class="com-card  action-info-card">
              <panelTitle style="margin: 0" title="工程" />
              <view class="details-body" style="padding-top: 10rpx;">
                <!-- 工程信息列表 -->
                <template v-if="state.showProjectList && state.showProjectList.length > 0">
                  <view class="details-list-card" v-for="(item, index) in state.showProjectList" :key="index">
                    <view class="card-row">
                      <text class="title">{{ item.projectName }}</text>
                      <text class="tag" v-if="props.type === 'receive'" :class="getTagClass(item.recStatusCode)">{{ getOperationStatus(item.recStatusCode) }}</text>
                    </view>
                    <view class="card-row">
                      <text class="label">工作负责人</text>
                      <text class="value">{{ item.wardUserName }}</text>
                    </view>

                    <view class="card-row">
                      <text class="label">备注信息</text>
                      <text class="value">{{ item.remark }}</text>
                    </view>
                    <view class="divider" v-if="index !== state.showProjectList.length - 1"></view>
                  </view>
                  <view class="more-btn" v-if="state.projectInfo.length > 2" @click="toggleProject">
                    <text>{{ state.showMoreProject ? '折叠' : '更多' }}</text>
                    <image :src="state.showMoreProject ? '/src/static/images/arrow-up.png' : '/src/static/images/arrow-down.png'" class="arrow-icon" />
                  </view>
                </template>
                
                <!-- 暂无数据 -->
                <template v-else>
                  <view class="no-data">
                    <text class="no-data-text">暂无数据</text>
                  </view>
                </template>
            </view>
          </view>
        </template>

        <!-- 审批信息区块 -->
        <template v-if="props.type === 'approve'">
          <view class="com-card action-info-card">
            <panelTitle title="审批信息" style="margin: 0" />

            <u-form-item label="审批意见" prop="approveOpinion" required>
              <view class="radio-group">
                <u-radio-group v-model="state.form.approveOpinion">
                  <u-radio :name="1" active-color="#2979ff">同意</u-radio>
                  <u-radio :name="0" active-color="#2979ff">驳回</u-radio>
                </u-radio-group>
              </view>
            </u-form-item>
            <u-form-item label="备注信息">
              <u-input v-model="state.form.approveRemark" placeholder="请输入" clearable />
            </u-form-item>
          </view>
        </template>
        
        <!-- 接收指令区块 -->
        <template v-if="props.type === 'receive'">
          
          <view class="com-card action-info-card">
            <panelTitle title="审批信息" style="margin: 0" />
            <view class="info-row">
              <text class="info-label">审批人</text>
              <text class="info-value">{{ state.approveInfo.auditUserName || '--' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">审批意见</text>
              <text class="info-value">{{ state.approveInfo.auditResult }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">审批时间</text>
              <text class="info-value">{{ state.approveInfo.auditTime || '--' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">备注信息</text>
              <text class="info-value">{{ state.approveInfo.remark || '--' }}</text>
            </view>
          </view>
        </template>
        
        <!-- 底部按钮 -->
        <view class="form-btn-group">
          <template v-if="props.type === 'approve'">
            <u-button class="form-sure-btn receive-btn" @click="handleApproveSubmit">提交</u-button>
          </template>
          <template v-else-if="props.type === 'receive'">
            <u-button class="form-sure-btn receive-btn" @click="handleReceiveSubmit">接收指令</u-button>
          </template>
          <template v-else>
            <u-button class="form-sure-btn receive-btn" @click="handleSubmit">提交</u-button>
          </template>
        </view>
      </u-form>
    </scroll-view>

    <u-select
      v-model="state.select.show"
      mode="single-column"
      :list="state.select.list"
      :default-value="getSelectDefaultValue()"
      @cancel="state.select.show = false"
      @confirm="onSelectConfirm"
    ></u-select>

    <MultiSelect
      v-model="state.showWorker"
      :selectOptions="state.userOptions"
      :defaultSelected="state.selectedWorkers"
      @onMultipleChoice="onMultipleChoice"
      @close="
        () => {
          state.showWorker = false
        }
      "
    />
    <u-picker
      v-model="state.showPickerTime"
      mode="time"
      :default-time="getDefaultTime()"
      :params="state.timeParams"
      @cancel="state.showPickerTime = false"
      @confirm="onTimeConfirm"
    ></u-picker>

    <!-- 确认弹窗 -->
    <ConfirmPopup
      :show="state.showConfirmPopup"
      @update:show="
        (val) => {
          state.showConfirmPopup = val
        }
      "
      @onConfirm="onPopupConfirm"
      popupTitle="确认提示"
      :title="state.confirmPopupTitle"
      description=""
      type="warning"
    />
  </view>
</template>
<script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import {
    getDispatchPage,
    getDispatchProjectList,
    getDispatchUser,
    getDeviceByProjectId,
    editRunInstruction,
    addRunInstruction,
    getRunInstructionDetails,
    approveRunInstruction,
    receiveRunInstruction,

  } from '../services'
  import { getOptions } from '@/api/common'
  import MultiSelect from '@/components/MultiSelect/index.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import dayjs from 'dayjs'
  import { useRouter } from 'uni-mini-router'

  const router = useRouter()

  const formRef = ref(null)
  const uNotifyRef = ref()

  const props = defineProps(['runCmdId', 'source', 'type'])

  // 计算是否为只读模式
  const isReadOnly = computed(() => {
    return props.type === 'approve' || props.type === 'receive'
  })

  // 计算是否为修改状态
  const isEdit = computed(() => {
    // 当type为approve或receive时不显示编辑模式
    if (props.type === 'approve' || props.type === 'receive') {
      return false
    }
    // 其他情况(修改、新增、复制)都显示编辑模式
    return true
  })

  const state = reactive({
    navTitle: '新增发起',
    schedulingScheme: undefined,
    dispatchOptions: [],
    dispatchTypeOptions: [],
    projectOptions: [],
    userOptions: [],
    managerOptions: [],
    projectManagerOptions: {}, // 存储各工程对应的负责人选项
    deviceOptions: [],
    showConfirmPopup: false,
    confirmPopupTitle: '',
    approveInfo: {
      auditUserName: '',
      auditResult: 1,
      auditTime: '',
      remark: ''
    },
    selectedWorkers: [], // 用于存储已选择的工作人员

    timeParams: {
      year: true,
      month: true,
      day: true,
      hour: true,
      minute: true,
    },

    select: {
      show: false,
      field: undefined,
      current1: { label: undefined, value: undefined },
      current2: { label: undefined, value: undefined },
      current3: { label: undefined, value: undefined },
      list: [],
    },
    showWorker: false,
    workerNames: '',
    openRangeOptions: [],
    showDeviceTime: false,
    deviceIndex: -1,
    pickType: '',
    timeSource: '',
    projectIndex: -1, // 当前操作的工程索引

    // 工程相关
    projectList: [],
    currentProjectIndex: -1,

    form: {
      cmdCode: '',
      cmdName: '',
      dispatchId: undefined,
      dispatchTypeCode: '',
      planEndDate: '',
      planStartDate: '',
      projectList: [],
      approveOpinion: 1, // 默认同意
      approveRemark: '',

    },
    rules: {
      cmdCode: [{ required: true, message: '请输入调度令编号' }],
      cmdName: [{ required: true, message: '请输入调度令名称' }],
      planStartDate: [{ required: true, message: '请选择计划开始时间' }],
      planEndDate: [{ required: true, message: '请选择计划结束时间' }],
      projectList: [{ required: true, message: '请至少添加一个工程' }],
      approveOpinion: [{ required: true, message: '请选择审批意见' }],
    },

    // 工程信息测试数据
    projectInfo: [
    ],
    showMoreProject: false,
    showProjectList: [],
    showPickerTime: false,
  })

  // 计算属性，根据是否展开显示不同数量的工程信息
  const showProjectList = computed(() => {
    return state.showMoreProject ? state.projectInfo : state.projectInfo.slice(0, 2)
  })

  // 展开/折叠工程信息
  const toggleProject = () => {
    state.showMoreProject = !state.showMoreProject
  }

  // 将计算属性添加到state中
  state.showProjectList = showProjectList

  // 获取日期选择器的默认时间
  const getDefaultTime = () => {
    if (state.pickType === 'start' && state.timeSource === 'plan' && state.form.planStartDate) {
      return state.form.planStartDate
    } else if (state.pickType === 'end' && state.timeSource === 'plan' && state.form.planEndDate) {
      return state.form.planEndDate
    }
    return dayjs().format('YYYY-MM-DD HH:mm')
  }

  // 获取u-select的默认选中索引
  const getSelectDefaultValue = () => {
    let defaultIndex = 0

    switch (state.select.field) {
      case 'schedulingScheme':
        if (state.select.current1.value && state.select.list.length > 0) {
          const index = state.select.list.findIndex(item => item.value === state.select.current1.value)
          defaultIndex = index >= 0 ? index : 0
        }
        break
      case 'dispatchTypeCode':
        if (state.select.current2.value && state.select.list.length > 0) {
          const index = state.select.list.findIndex(item => item.value === state.select.current2.value)
          defaultIndex = index >= 0 ? index : 0
        }
        break
      case 'project':
        if (state.currentProjectIndex >= 0 && state.projectList[state.currentProjectIndex]?.projectId && state.select.list.length > 0) {
          const index = state.select.list.findIndex(item => item.value === state.projectList[state.currentProjectIndex].projectId)
          defaultIndex = index >= 0 ? index : 0
        }
        break
      case 'manager':
        if (state.currentProjectIndex >= 0 && state.projectList[state.currentProjectIndex]?.wardUserId && state.select.list.length > 0) {
          const index = state.select.list.findIndex(item => item.value === state.projectList[state.currentProjectIndex].wardUserId)
          defaultIndex = index >= 0 ? index : 0
        }
        break
      default:
        defaultIndex = 0
    }

    return [defaultIndex]
  }

  // 根据状态获取标签样式类
  const getTagClass = (status) => {
    switch (status) {
      case 0:
        return 'tag-pending'
      case 1:
        return 'tag-completed'
      default:
        return 'tag-unknown'
    }
  }

  // 根据状态码获取操作状态文本
  const getOperationStatus = (status) => {
    console.log(status)
    switch (status) {
      case 0:
        return '暂未接收'
      case 1:
        return '已接收'
      default:
        return '未知'
    }
  }

  onMounted(() => {
    formRef.value.setRules(state.rules)

    // 设置页面标题
    if (props.type === 'approve') {
      state.navTitle = '审批指令'
    } else if (props.type === 'receive') {
      state.navTitle = '接收指令'
    } else if (props?.runCmdId) {
      if (props.source == 'copy') {
        state.navTitle = '复制'
      } else {
        state.navTitle = '修改'
      }
    }

    // 获取调度类型
    getOptions('dispatchCmdDispatchType').then(res => {
      state.dispatchTypeOptions = (res.data || []).map(el => ({
        label: el.value,
        value: el.key
      }))

      // 如果是修改模式，加载详情后设置调度类型
      if (props?.runCmdId && state.form.dispatchTypeCode) {
        const typeOption = state.dispatchTypeOptions.find(item => item.value === state.form.dispatchTypeCode)
        if (typeOption) {
          state.select.current2 = {
            label: typeOption.label,
            value: typeOption.value
          }
        }
      }
    })

    // 使用mock数据替代接口调用
    state.dispatchOptions = [
      { label: 'FS2024001', value: '1', dispatchId: '1', dispatchCode: 'FS2024001' },
      { label: 'GG2024001', value: '2', dispatchId: '2', dispatchCode: 'GG2024001' }
    ]

    state.projectOptions = [
      { label: '河道清淤工程', value: '1', projectId: '1', dispatchProjectId: '1', projectName: '河道清淤工程' },
      { label: '水闸维护工程', value: '2', projectId: '2', dispatchProjectId: '2', projectName: '水闸维护工程' }
    ]

    // 如果是修改模式，加载详情
    if (props?.runCmdId) {
      // 使用mock数据
      const mockDetail = {
        cmdCode: 'DD202401001',
        cmdName: '春季防汛调度指令',
        dispatchId: '1',
        dispatchCode: 'FS2024001',
        dispatchTypeCode: '1',
        planStartDate: '2024-03-15 08:00',
        planEndDate: '2024-03-15 18:00',
        projectList: [
          {
            projectId: '1',
            projectName: '河道清淤工程',
            wardUserId: '1',
            wardUserName: '张三',
            remark: '注意安全操作'
          }
        ]
      }

      state.form = mockDetail
      state.projectList = mockDetail.projectList || []

      // 设置调度方案显示值
      const dispatchOption = state.dispatchOptions.find(item => item.value === mockDetail.dispatchId)
      if (dispatchOption) {
        state.select.current1 = {
          label: dispatchOption.label,
          value: dispatchOption.value
        }
      }

      // 设置调度类型显示值
      state.select.current2 = {
        label: '防汛调度',
        value: '1'
      }

      if (props.type === 'approve' || props.type === 'receive') {
        state.projectInfo = mockDetail.projectList
      }

      // 如果是接收指令页面，加载审批信息
      if (props.type === 'receive') {
        state.approveInfo = {
          auditUserName: '李四',
          auditResult: '同意',
          auditTime: '2024-03-15 07:30:00',
          remark: '同意执行该调度指令'
        }
      }
    }
  })

  const onSelectConfirm = item => {
    switch (state.select.field) {
      case 'schedulingScheme':
        state.select.current1 = item[0]
        state.form.dispatchId = item[0].value
        return
      case 'dispatchTypeCode':
        state.select.current2 = item[0]
        state.form.dispatchTypeCode = item[0].value
        return
      case 'project':
        if (state.currentProjectIndex >= 0) {
          // 从选项中找到对应的完整项目数据
          const selectedProject = state.projectOptions.find(project => project.value === item[0].value)

          state.projectList[state.currentProjectIndex].projectId = item[0].value
          state.projectList[state.currentProjectIndex].projectName = item[0].label
          state.projectList[state.currentProjectIndex].dispatchProjectId = selectedProject?.dispatchProjectId
          // 清空工程负责人
          state.projectList[state.currentProjectIndex].wardUserId = ''
          state.projectList[state.currentProjectIndex].wardUserName = ''
          // 强制更新数组，触发视图更新
          state.projectList = [...state.projectList]
          // 更新表单验证
          state.form.projectList = [...state.projectList]

          // 获取工程负责人 - 使用mock数据
          state.managerOptions = [
            { label: '张三', value: '1' },
            { label: '李四', value: '2' },
            { label: '王五', value: '3' }
          ]
        }
        state.currentProjectIndex = -1
        return
      case 'manager':
        if (state.currentProjectIndex >= 0) {
          state.projectList[state.currentProjectIndex].wardUserId = item[0].value
          state.projectList[state.currentProjectIndex].wardUserName = item[0].label
          // 强制更新数组，触发视图更新
          state.projectList = [...state.projectList]
          // 更新表单验证
          state.form.projectList = [...state.projectList]
        }
        state.currentProjectIndex = -1
        return
      default:
        return
    }
  }

  // 添加工程
  const addProject = () => {
    state.projectList.push({
      projectId: '',
      projectName: '',
      wardUserId: '',
      wardUserName: '',
      remark: ''
    })
    // 更新表单验证
    state.form.projectList = [...state.projectList]
  }

  // 删除工程
  const deleteProject = (index) => {
    state.projectList.splice(index, 1)
    // 更新表单验证
    state.form.projectList = [...state.projectList]
  }

  // 选择工程
  const selectProject = (index) => {
    if (isReadOnly.value) return
    state.currentProjectIndex = index
    state.select.show = true
    state.select.field = 'project'
    state.select.list = state.projectOptions
  }

  // 选择负责人
  const selectManager = (index) => {
    if (isReadOnly.value) return
    state.currentProjectIndex = index

    // 获取当前选中工程的负责人列表
    const currentProject = state.projectList[index]

    if (currentProject && currentProject.projectId) {
      // 使用mock数据
      state.managerOptions = [
        { label: '张三', value: '1' },
        { label: '李四', value: '2' },
        { label: '王五', value: '3' }
      ]
      state.select.show = true
      state.select.field = 'manager'
      state.select.list = state.managerOptions
    } else {
      uni.showToast({
        title: '请先选择工程',
        icon: 'none',
        duration: 2000
      })
    }
  }

  const onOpenTimePicker = (type, source, idx) => {
    state.pickType = type
    state.timeSource = source
    state.showPickerTime = true
  }

  const onTimeConfirm = p => {
    const timeStr = `${p.year}-${p.month}-${p.day} ${p.hour}:${p.minute}`

    if (state.timeSource === 'plan') {
      if (state.pickType === 'start') {
        state.form.planStartDate = timeStr
      }
      if (state.pickType === 'end') {
        timeComparison(state.form.planStartDate, timeStr)
        state.form.planEndDate = timeStr
      }
    }


  }

  const timeComparison = (startTime, endTime) => {
    if (endTime < startTime) {
      uni.showToast({
        title: '结束时间不能小于开始时间',
        icon: 'none',
        duration: 2000
      })
      if (state.timeSource === 'plan') {
        state.form.planEndDate = ''
      }
      return
    }
  }

  // 审批提交
  const handleApproveSubmit = () => {
    formRef.value.validate(valid => {
      if (valid) {
        state.showConfirmPopup = true
        state.confirmPopupTitle = '是否确认提交审批?'
      }
    })
  }

  // 接收指令提交
  const handleReceiveSubmit = () => {
    state.showConfirmPopup = true
    state.confirmPopupTitle = '是否确认接收指令?'
  }

  // 确认弹窗确认
  const onPopupConfirm = () => {
    if (props.type === 'approve') {
      // 提交审批 - 使用mock逻辑
      uNotifyRef.value.show({
        type: 'success',
        title: '审批成功',
        duration: 800,
      })
      setTimeout(() => {
        router.push({
          path: '/pages/dispatch-instruction/index',
        })
      }, 800)
    } else if (props.type === 'receive') {
      // 接收指令 - 使用mock逻辑
      uNotifyRef.value.show({
        type: 'success',
        title: '接收成功',
        duration: 800,
      })
      setTimeout(() => {
        router.push({
          path: '/pages/dispatch-instruction/index',
        })
      }, 800)
    }
    state.showConfirmPopup = false
  }

  const handleSubmit = () => {
    // 首先验证工程信息的必填项
    if (state.projectList.length === 0) {
      uni.showToast({
        title: '请至少添加一个工程',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 验证每个工程的必填字段
    for (let i = 0; i < state.projectList.length; i++) {
      const project = state.projectList[i]
      if (!project.projectName) {
        uni.showToast({
          title: `第${i + 1}个工程的工程名称不能为空`,
          icon: 'none',
          duration: 2000
        })
        return
      }
      if (!project.wardUserName) {
        uni.showToast({
          title: `第${i + 1}个工程的工程负责人不能为空`,
          icon: 'none',
          duration: 2000
        })
        return
      }

      if (!project.remark || project.remark.trim() === '') {
        uni.showToast({
          title: `第${i + 1}个工程的备注不能为空`,
          icon: 'none',
          duration: 2000
        })
        return
      }
    }

    // 将工程列表赋值给表单
    state.form.projectList = state.projectList

    formRef.value.validate(valid => {
      if (valid) {
        // 使用mock提交逻辑
        const isEdit = props?.runCmdId && props.source !== 'copy'

        uNotifyRef.value.show({
          type: 'success',
          title: isEdit ? '修改成功' : '新增成功',
          duration: 800,
        })
        setTimeout(() => {
          router.push({
            path: '/pages/dispatch-instruction/index',
          })
        }, 800)
      }
    })
  }

  const handleCancel = () => {
    router.push({
      path: '/pages/dispatch-instruction/index',
    })
  }

  // 多选处理函数
  const onMultipleChoice = (selectedItems) => {
    state.selectedWorkers = selectedItems
  }
</script>
<style lang="scss" scoped>
  .special-form-item {
    :deep(.u-form-item__body) {
      display: block;
    }
    .checkbox-label {
      margin-right: 30rpx;
    }
    :deep(.uni-checkbox-input) {
      margin-right: 0rpx;
    }
  }
  .textarea {
    :deep(.u-form-item__body) {
      display: block;
    }
    :deep(.u-form-item--left) {
      width: 100% !important;
    }
    :deep(.u-form-item--right) {
      height: 268rpx;
      border-radius: 8rpx;
      background: #f2f3f5;
      border: 1px solid #e5e6eb;
    }
  }

  .radio-group {
    display: flex;
    flex-direction: row;

    :deep(.u-radio) {
      margin-right: 40rpx;
    }
  }

  .info-row {
    display: flex;
    flex-direction: row;
    padding: 20rpx 0;
    border-bottom: 1px solid #e5e6eb;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      width: 210rpx;
      color: #86909c;
      font-size: 28rpx;
    }

    .info-value {
      flex: 1;
      color: #1d2129;
      font-size: 28rpx;
    }
  }

  .form-container {
    position: relative;
  }

  .status-icon {
    position: absolute;
    top: 0rpx;
    right: 20rpx;
    width: 160rpx;
    height: 160rpx;
    z-index: 100;
  }

  .receive-btn {
    width: calc(100% - 60rpx) !important;
    border-radius: 60rpx;
  }

  .details-list-card {
    .divider {
      height: 2rpx;
      border-top: 1px dashed #E5E6EB;
      margin-top: 16rpx ;
	  margin-bottom: 13rpx;
    }
  }

  .more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    color: #3772FF;
    font-size: 28rpx;

    .arrow-icon {
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
    }
  }

  .add-project-item {
    :deep(.u-form-item__body) {
      display: block;
    }

    :deep(.u-form-item__label) {
      display: none;
    }
  }

  .add-btn {
      width: 100%;
      border-radius: 8rpx;
      color: #3772FF;
      text-align: right;

      .add-text {
        color: #3772FF;
        font-size: 28rpx;
      }
    }

  .project-list {

    .project-item {
      background: #F7F8FA;
      border-radius: 12rpx;
      padding: 10rpx 24rpx;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .project-row {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.project-name-row {
        justify-content: space-between;
      }
    }

    .project-label {
      width: 160rpx;
      color: #1D2129;
      font-size: 28rpx;
      margin-right: 20rpx;
    }

    .project-input,
    .project-name-input {
      flex: 1;
    }

    .delete-icon {
      margin-left: 20rpx;
    }

    .project-divider {
      height: 1px;
      background: #E5E6EB;
      margin: 24rpx 0;
    }
  }

  .no-data {
    text-align: center;
    padding: 60rpx 0;

    .no-data-text {
      color: #86909C;
      font-size: 28rpx;
    }
  }
  .action-info-card {
    background: linear-gradient(to bottom, #E9F4FF 3%, #ffffff 18%);
  }
  .tag {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #fff;
}

.tag-received {
  background-color: #3772FF !important;
}

.tag-pending {
  background-color: #FFECE8 !important;
    color: #F53F3F !important;
    border-color: #F53F3F !important;
}

.tag-processing {
  background-color: #FF9500 !important;
}

.tag-unknown {
  background-color: #F5222D !important;
}
</style>
