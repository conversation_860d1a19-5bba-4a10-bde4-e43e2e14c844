import { request } from '@/utils/request'

//调度指令--列表
export function getDispatchInstruction(data: object) {
  return request({
    url: '/custom/dispatch-cmd/page',
    method: 'post',
    data,
  })
}

// 调度指令--增加
export function addDispatchInstruction(data: object) {
  return request({
    url: '/custom/dispatch-cmd/add',
    method: 'post',
    data,
  })
}

//调度列表
export function getDispatchPage(data: object) {
  return request({
    url: '/custom/dispatch/page',
    method: 'post',
    data,
  })
}

//调度工程列表
export function getDispatchProjectList(params: object) {
  return request({
    url: '/custom/dispatch-project/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//调度用户
export function getDispatchUser(params: object) {
  return request({
    url: '/custom/dispatch-project/user/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程下的设备
export function getDeviceByProjectId(params: object) {
  return request({
    url: '/custom/dispatch-cmd/getDeviceList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 详情
export function getDispatchInstructionDetails(params: object) {
  return request({
    url: '/custom/dispatch-cmd/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 更新
export function editDispatchInstruction(data: object) {
  return request({
    url: '/custom/dispatch-cmd/update',
    method: 'post',
    data,
  })
}

// 删除
export function deleteDispatchInstruction(params: object) {
  return request({
    url: '/custom/dispatch-cmd/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 审批调度指令
export function approveDispatchInstruction(data: object) {
  return request({
    url: '/custom/dispatch-cmd/audit',
    method: 'post',
    data,
  })
}

// 接收调度指令
export function receiveDispatchInstruction(params: string) {
  return request({
    url: '/custom/dispatch-cmd/rec',
    method: 'post',
    params,
  })
}

// 操作指令-----列表分页查询
export function getOperationInstruction(data: object) {
  return request({
    url: '/custom/operation-cmd/page',
    method: 'post',
    data,
  })  
}

// 操作指令-----详情
export function getOperationInstructionDetails(params: object) {
  return request({
    url: '/custom/operation-cmd/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 操作指令--增加
export function addOperationInstruction(data: object) {
  return request({
    url: '/custom/operation-cmd/add',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
