import { request } from '@/utils/request'

//运行指令--列表
export function getRunInstruction(data: object) {
  return request({
    url: '/custom/runmd/page',
    method: 'post',
    data,
  })
}

// 运行指令--增加
export function addRunInstruction(data: object) {
  return request({
    url: '/custom/runmd/add',
    method: 'post',
    data,
  })
}

//调度列表
export function getDispatchPage(data: object) {
  return request({
    url: '/custom/dispatch/page',
    method: 'post',
    data,
  })
}

//调度工程列表
export function getDispatchProjectList(params: object) {
  return request({
    url: '/custom/dispatch-project/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//调度用户
export function getDispatchUser(params: object) {
  return request({
    url: '/custom/dispatch-project/user/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程下的设备
export function getDeviceByProjectId(params: object) {
  return request({
    url: '/custom/runmd/getDeviceList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 详情
export function getRunInstructionDetails(params: object) {
  return request({
    url: '/custom/runmd/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 更新
export function editRunInstruction(data: object) {
  return request({
    url: '/custom/runmd/update',
    method: 'post',
    data,
  })
}

// 删除
export function deleteRunInstruction(params: object) {
  return request({
    url: '/custom/runmd/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 审批运行指令
export function approveRunInstruction(data: object) {
  return request({
    url: '/custom/runmd/audit',
    method: 'post',
    data,
  })
}

// 接收运行指令
export function receiveRunInstruction(params: string) {
  return request({
    url: '/custom/runmd/rec',
    method: 'post',
    params,
  })
}

// 操作票-----列表分页查询
export function getOperateTicket(data: object) {
  return request({
    url: '/custom/operateCmd/page',
    method: 'post',
    data,
  })
}

// 操作票-----详情
export function getOperateTicketDetails(params: object) {
  return request({
    url: '/custom/operateCmd/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 操作指令--增加
export function addOperateCmd(data: object) {
  return request({
    url: '/custom/operateCmd/add',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
