<template>
  <view class="page-nav-top-common-bg1">
    <NavBar title="操作指令" :background="{ backgroundColor: 'transparent' }">
      <template #right>
        <text class="list-add-btn" style="font-size: 28rpx;border-radius: 10rpx;background-color: #007AFF;color: #fff;padding: 10rpx 20rpx;" @click="add">新增+</text>
      </template>
    </NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
    <view class="list-search">
      <u-input
        class="search-input"
        v-model="state.keyword"
        placeholder="请输入操作令编号"
        @input="handleSearch"
        @confirm="handleSearch"
      ></u-input>
      <text class="search-icon" @click="handleSearch"></text>
      <text class="search-close" @click="handleClose"></text>
    </view>
  </view>
  <ZPaging ref="pagingRef" :style="{ marginTop: `calc(24px + ${userStore.statusBarHeight}px + 120rpx)`, paddingTop: '28rpx', zIndex: 10 }" v-model="state.list" @query="getList">
    <view class="com-card" @click="handleOperationDetails(el)" v-for="(el, idx) in state.list" :key="idx">
      <view class="card-title">{{ el.operateCode }}</view>
      <view class="grey-card">
        <view class="card-row">
          <text class="label">工程名称</text>
          <text class="value">{{ el.projectName || '--' }}</text>
        </view>
        <view class="card-row">
          <text class="label">操作日期</text>
          <text class="value">{{ el.operateDate }}</text>
        </view>
        <view class="card-row">
          <text class="label">操作人</text>
          <text class="value">{{ el.operateName }}</text>
        </view>
        <view class="card-row">
          <text class="label">负责人</text>
          <text class="value">{{ el.guardianName }}</text>
        </view>
        <view class="card-row">
          <text class="label">操作开始时间</text>
          <text class="value">{{ el.startDate }}</text>
        </view>
        <view class="card-row">
          <text class="label">操作结束时间</text>
          <text class="value">{{ el.endDate }}</text>
        </view>
      </view>
    </view>
  </ZPaging>
</template>
<script setup>
  import { reactive, ref, onMounted, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'

  import * as _ from 'lodash'
  import { getOperationInstruction } from '../services'

  const router = useRouter()

  const pagingRef = ref(null)

  const props = defineProps(['cmdCode'])

  const userStore = useUserStore()

  const state = reactive({
    keyword: '',
    list: [],
  })

  const handleSearch = _.debounce(val => {
    nextTick(() => {
      pagingRef.value.reload()
    })
  }, 500)
  const handleClose = () => {
    state.keyword = ''
    nextTick(() => {
      pagingRef.value.reload()
    })
  }

  // z-paging 会自动调用 getList，无需手动初始化
  const getList = async (pageNo, pageSize) => {
    try {
      // 使用mock数据进行测试
      const mockData = [
        {
          operateCmdId: '1',
          operateCode: 'CZ202401001',
          projectName: '河道清淤工程',
          operateDate: '2024-03-15',
          operateName: '赵六',
          guardianName: '张三',
          startDate: '2024-03-15 08:30',
          endDate: '2024-03-15 17:30'
        },
        {
          operateCmdId: '2',
          operateCode: 'CZ202401002',
          projectName: '水闸维护工程',
          operateDate: '2024-03-15',
          operateName: '钱七',
          guardianName: '李四',
          startDate: '2024-03-15 09:00',
          endDate: '2024-03-15 17:00'
        }
      ]

      const list = mockData.filter(item => 
        !state.keyword || 
        item.operateCode.includes(state.keyword) ||
        item.projectName.includes(state.keyword)
      )
      
      state.list = list
      pagingRef.value.complete(list)
      
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    } catch (error) {
      console.error('获取操作指令列表失败:', error)
      pagingRef.value.complete([])
      uni.showToast({
        title: '网络错误，请稍后重试',
        icon: 'none'
      })
    }
  }

  //详情
  const handleOperationDetails = row => {
    router.push({
      path: '/pages/dispatch-instruction/operation-ticket-details/index',
      query: { operateCmdId: row.operateCmdId },
    })
  }

    //新增
  const add = () => {
    router.push({
      path: '/pages/dispatch-instruction/operation-ticket-add/index',
      query: { runCmd: props.cmdCode },
    })
  }

  
</script>
<style lang="scss" scoped></style>
